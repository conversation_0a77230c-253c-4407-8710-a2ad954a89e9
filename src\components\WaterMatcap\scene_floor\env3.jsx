import { Environment, Lightformer } from "@react-three/drei";
import { useThree } from "@react-three/fiber";
import * as THREE from "three";

import { mainCamera } from "./cameras3";

export function Env() {
  const camera = useThree((state) => state.camera);

  const background = camera === mainCamera ? true : true;

  return (
    <Environment
      resolution={1024}
      background={background}
      frames={1}
      environmentIntensity={1}
    >
      <Room />
    </Environment>
  );
}

const box = new THREE.BoxGeometry();
const white = new THREE.MeshStandardMaterial({
  color: new THREE.Color(1, 1, 1)
});

function Room() {
  return (
    <group position={[0, -0.5, 0]}>
      {/* Room, just an inverted box */}
      <mesh
        geometry={box}
        castShadow
        receiveShadow
        position={[0.0, 13.2, 0.0]}
        scale={[31.5, 28.5, 31.5]}
      >
        <meshStandardMaterial color="gray" side={THREE.BackSide} />
      </mesh>
      {/* Some boxes */}
      <mesh
        geometry={box}
        material={white}
        castShadow
        receiveShadow
        position={[-10.906, -1.0, 1.846]}
        rotation={[0, -0.195, 0]}
        scale={[2.328, 7.905, 4.651]}
      />
      <mesh
        geometry={box}
        material={white}
        castShadow
        receiveShadow
        position={[-5.607, -0.754, -0.758]}
        rotation={[0, 0.994, 0]}
        scale={[1.97, 1.534, 3.955]}
      />
      <mesh
        geometry={box}
        material={white}
        castShadow
        receiveShadow
        position={[6.167, -0.16, 7.803]}
        rotation={[0, 0.561, 0]}
        scale={[3.927, 6.285, 3.687]}
      />
      <mesh
        geometry={box}
        material={white}
        castShadow
        receiveShadow
        position={[-2.017, 0.018, 6.124]}
        rotation={[0, 0.333, 0]}
        scale={[2.002, 4.566, 2.064]}
      />
      <mesh
        geometry={box}
        material={white}
        castShadow
        receiveShadow
        position={[2.291, -0.756, -2.621]}
        rotation={[0, -0.286, 0]}
        scale={[1.546, 1.552, 1.496]}
      />
      <mesh
        geometry={box}
        material={white}
        castShadow
        receiveShadow
        position={[-2.193, -0.369, -5.547]}
        rotation={[0, 0.516, 0]}
        scale={[3.875, 3.487, 2.986]}
      />

      {/* Example Lightformers and lights (uncomment as needed) */}
      {/*<Lightformer
        form="box"
        intensity={80}
        position={[-14.0, 14.0, -12.0]}
        scale={[0.1, 40.5, 10.5]}
        target={false}
      />*/}

      {/* ORIGINAL = 80  */}
      <Lightformer
        form="box"
        intensity={800} 
        position={[-15.0, 16.0, -12.0]}
        scale={[0.1, 10.5, 10.5]}
        rotation={[0, Math.PI * -0.3, 0]}
        target={false}
      />

      {/*<Lightformer
        form="box"
        intensity={10}
        position={[10.0, 14.0, -12.0]}
        scale={[0.1, 20.5, 10.5]}
        rotation={[0, Math.PI * 0.5, 0]}
        target={false}
      />*/}
      <pointLight
        intensity={100}
        decay={2}
        distance={0.5}
        position={[14, 12.0, -10.0]}
      />
      {/* <pointLight intensity={200} decay={2} position={[2, 12.0, -12.0]} /> */}
      {/* ...other commented lights... */}
    </group>
  );
}